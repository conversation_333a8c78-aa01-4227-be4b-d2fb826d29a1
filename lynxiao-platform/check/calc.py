import numpy as np


def cosine_similarity(vec1, vec2):
    """
    计算两个向量的余弦相似度

    :param vec1: 第一个浮点型数组
    :param vec2: 第二个浮点型数组
    :return: 余弦相似度值
    """
    vec1 = np.array(vec1)
    vec2 = np.array(vec2)

    # 计算分子：向量点积
    dot_product = np.dot(vec1, vec2)

    # 计算分母：向量的模长乘积
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)

    # 避免除以零
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0.0

    # 计算余弦相似度
    return dot_product / (norm_vec1 * norm_vec2)
