import glob
import json
import logging
import multiprocessing as mp
import os
import re
import sys
import time

import requests
from requests.exceptions import RequestException

REQUEST_TIMEOUT = 3  # 请求超时时间
RETRY_COUNT = 10  # 失败重试次数

# 预编译正则
content_re = re.compile(r'"content"\s*:\s*"((?:\\.|[^"\\])*)"')

# 加载配置文件
config_path = sys.argv[1]
# config_path = (
#     "/feng/Projects/Other/data_process/m3_build/http_embedding_py/conf/config-med.json"
# )
config = json.load(open(config_path, "r", encoding="utf-8"))


def get_embedding(url: str, text: str):
    """调用向量API获取嵌入向量"""
    payload = {
        "header": {"traceId": "feng-test"},
        "payload": {"texts": [text]},
    }

    for attempt in range(RETRY_COUNT):
        try:
            response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.json()["payload"]["embedding"][0]  # 根据实际API响应结构调整
        except RequestException as e:
            logging.error(
                f"API请求失败，尝试次数 {attempt + 1}/{RETRY_COUNT}: {e}, URL: {url}, Payload: {payload}"
            )
            time.sleep(2**attempt)  # Exponential backoff
    return None


def get_embedding_v2(url: str, text: str):
    """调用向量API获取嵌入向量"""
    payload = {
        "sid": "xxxxx",
        "norm": True,
        "texts": [text],
    }

    for attempt in range(RETRY_COUNT):
        try:
            response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.json()["embeddings"][0][
                "embedding"
            ]  # 根据实际API响应结构调整
        except RequestException as e:
            logging.error(
                f"API请求失败，尝试次数 {attempt + 1}/{RETRY_COUNT}: {e}, URL: {url}, Payload: {payload}"
            )
            time.sleep(2**attempt)  # Exponential backoff
    return None


def get_token(url: str, text: str) -> str:
    """调用向量API获取嵌入向量"""
    payload = {
        "header": {"traceId": "11"},
        "payload": {
            "data": [
                {
                    "id": 1,
                    "title": text,
                }
            ]
        },
    }

    for attempt in range(RETRY_COUNT):
        try:
            response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.json()["payload"]["data"][0]["title"]
        except RequestException as e:
            logging.error(
                f"API请求失败，尝试次数 {attempt + 1}/{RETRY_COUNT}: {e}, URL: {url}, Payload: {payload}"
            )
            time.sleep(2**attempt)  # Exponential backoff
    return None


def process(src_path: str, milvus_path: str, es_path: str) -> int:
    count = 0
    token_urls = config["token_urls"]
    embedding_urls = config["embedding_urls"]

    with open(src_path, "r", encoding="utf-8") as src, open(
        milvus_path, "w", encoding="utf-8"
    ) as milvus_file, open(es_path, "w", encoding="utf-8") as es_file:
        for line_num, line in enumerate(src, 1):
            try:
                count += 1
                embedding_url = (
                    embedding_urls[count % len(embedding_urls)] + "/embedding/api/v2"
                )
                token_url = token_urls[count % len(token_urls)] + "/seg/api/v2/objs"
                data = json.loads(line)

                data["title_terms"] = get_token(token_url, data["title"])

                for id, span in enumerate(data["ss"]):
                    content = extract_and_truncate(
                        line, span["span"][0], span["span"][1]
                    )
                    combined = f"{data['title']}\n{content}"

                    embedding = get_embedding(embedding_url, combined)

                    data["ss"][id]["embedding"] = embedding
                    data["ss"][id]["content_terms"] = get_token(token_url, content)

                    milvus_item = {
                        "id": span["id"],
                        "did": data["_id"],
                        "embedding": embedding,
                    }
                    milvus_file.write(
                        json.dumps(milvus_item, ensure_ascii=False) + "\n"
                    )

                # 添加向量到数据并保存
                es_file.write(json.dumps(data, ensure_ascii=False) + "\n")

            except json.JSONDecodeError:
                logging.warning(f"行 {line_num}: JSON解析失败")
            except Exception as e:
                logging.error(f"行 {line_num}: 发生错误 - {str(e)}")

    return count


def process_v2(src_path: str, milvus_path: str, es_path: str) -> int:
    count = 0
    token_urls = config["token_urls"]
    embedding_urls = config["embedding_urls"]

    with open(src_path, "r", encoding="utf-8") as src, open(
        milvus_path, "w", encoding="utf-8"
    ) as milvus_file, open(es_path, "w", encoding="utf-8") as es_file:
        for line_num, line in enumerate(src, 1):
            try:
                count += 1
                embedding_url = (
                    embedding_urls[count % len(embedding_urls)] + "/v1/semantic"
                )
                token_url = token_urls[count % len(token_urls)] + "/seg/api/v2/objs"
                data = json.loads(line)

                data["title_terms"] = get_token(token_url, data["title"])

                for id, span in enumerate(data["ss"]):
                    content = extract_and_truncate(
                        line, span["span"][0], span["span"][1]
                    )
                    combined = f"{data['title']}\n{content}"

                    embedding = get_embedding_v2(embedding_url, combined)

                    data["ss"][id]["embedding"] = embedding
                    data["ss"][id]["content_terms"] = get_token(token_url, content)

                es_item = {
                    "id": data["_id"],
                    "title": data["title"],
                    "content": data["content"],
                    "title_terms": data["title_terms"],
                    "ss": data["ss"],
                    "s": 0,
                }

                es_file.write(json.dumps(es_item, ensure_ascii=False) + "\n")
                milvus_file.write(json.dumps(data, ensure_ascii=False) + "\n")

            except json.JSONDecodeError:
                logging.warning(f"行 {line_num}: JSON解析失败")
            except Exception as e:
                logging.error(f"行 {line_num}: 发生错误 - {str(e)}")

    return count


def es_build(src_path: str, mongo_path: str) -> int:
    """
    仅构建mongo数据即可，es数据是mongo数据的子集
    """
    count = 0
    token_urls = config["token_urls"]
    embedding_urls = config["embedding_urls"]

    with open(src_path, "r", encoding="utf-8") as src, open(
        mongo_path, "w", encoding="utf-8"
    ) as dst:
        for line_num, line in enumerate(src, 1):
            try:
                count += 1
                embedding_url = (
                    embedding_urls[count % len(embedding_urls)] + "/v1/semantic"
                )
                token_url = token_urls[count % len(token_urls)] + "/seg/api/v2/objs"
                mongo_item = json.loads(line)

                mongo_item["title_terms"] = get_token(token_url, mongo_item["title"])

                # 通用使用，医疗使用\n
                combined = f"{mongo_item['title']}\n{mongo_item['content']}"

                embedding = get_embedding_v2(embedding_url, combined)

                ss = [
                    {
                        "id": mongo_item["_id"],
                        "span": [0, len(mongo_item["content"])],
                        "embedding": embedding,
                        "content_terms": get_token(token_url, mongo_item["content"]),
                    }
                ]

                mongo_item["ss"] = ss

                dst.write(json.dumps(mongo_item, ensure_ascii=False) + "\n")

            except json.JSONDecodeError:
                logging.warning(f"行 {line_num}: JSON解析失败")
            except Exception as e:
                logging.error(f"行 {line_num}: 发生错误 - {str(e)}")

    return count


def extract_and_truncate(item: str, start: int, end: int) -> str:
    match = content_re.search(item)
    if match:
        raw_content = match.group(1)
        truncated = raw_content[start : min(len(raw_content), end)]

        return truncated


def es_build_no_token(src_path: str, mongo_path: str) -> int:
    """
    仅替换向量数据为医疗向量
    仅构建mongo数据即可，es数据是mongo数据的子集
    """
    count = 0
    embedding_urls = config["embedding_urls"]

    with open(src_path, "r", encoding="utf-8") as src, open(
        mongo_path, "w", encoding="utf-8"
    ) as dst:
        for line_num, line in enumerate(src, 1):
            try:
                count += 1
                embedding_url = (
                    embedding_urls[count % len(embedding_urls)] + "/embedding/api/v2"
                )
                mongo_item = json.loads(line)
                # 通用使用，医疗使用\n
                combined = f"{mongo_item['title']}\n{mongo_item['content']}"
                mongo_item["ss"][0]["embedding"] = get_embedding(embedding_url, combined)

                dst.write(json.dumps(mongo_item, ensure_ascii=False) + "\n")

            except json.JSONDecodeError:
                logging.warning(f"行 {line_num}: JSON解析失败")
            except Exception as e:
                logging.error(f"行 {line_num}: 发生错误 - {str(e)}")

    return count

if __name__ == "__main__":
    src_dir = config["src_dir"]
    mongo_dir = config["mongo_dir"]
    worker_num = config["worker_num"]

    os.makedirs(mongo_dir, exist_ok=True)

    task_name = src_dir.split("/")[-1]

    # 配置日志记录
    log_file = f"logs/{task_name}.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,  # 设置日志级别
        format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    )

    logging.info(f"{task_name} starting...\n")
    logging.info(f"config: {config}\n")

    # 遍历所有jsons文件
    src_paths = glob.glob(os.path.join(src_dir, "*.json"))
    mongo_paths = [
        os.path.join(mongo_dir, os.path.basename(src_path)) for src_path in src_paths
    ]

    with mp.Pool(worker_num) as pool:
        async_res = pool.starmap_async(es_build_no_token, zip(src_paths, mongo_paths))
        counts = async_res.get()

    total = sum(counts)

    logging.info(f"{task_name} finished, total: {total}\n")
