"""
 <AUTHOR> 2025/8/18 10:06
"""


from openai import OpenAI

client = OpenAI(
    api_key="ms-c02f44ee-70af-47fa-be97-7d606aba5535", # 请替换成您的ModelScope Access Token
    base_url="https://api-inference.modelscope.cn/v1/"
)


response = client.chat.completions.create(
    model="Qwen/Qwen2.5-Coder-32B-Instruct", # ModleScope Model-Id
    messages=[
        {
            'role': 'system',
            'content': 'You are a helpful assistant.'
        },
        {
            'role': 'user',
            'content': '用python写一下快排'
        }
    ],
    stream=False
)
# print(response.choices[0].message.content)
for chunk in response:
    print(chunk.choices[0].delta.content, end='', flush=True)