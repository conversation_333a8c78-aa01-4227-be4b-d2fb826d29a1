"""
 <AUTHOR> 2025/4/28 14:56
"""

import json
import os
import time
import requests
import concurrent.futures
from typing import List, Dict, Any

# 配置线程数
MAX_WORKERS = 16


def read_ldoc_ids(file_path: str) -> List[str]:
    """
    读取文件中的每一行，提取id字段形成列表
    """
    ldoc_ids = []
    
    # 确保目录和文件存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return ldoc_ids
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    record = json.loads(line)
                    if "id" in record:
                        ldoc_ids.append(record["id"])
                except json.JSONDecodeError:
                    print(f"无法解析JSON记录: {line[:100]}...")
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
    
    return ldoc_ids


def ensure_output_directory(output_file: str):
    """
    确保输出目录存在
    """
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    return output_file


def fetch_and_save_batch(batch: List[str], trace_id: str, api_url: str, output_file: str) -> List[Dict[str, Any]]:
    """
    发送一批ldoc_ids到API，并立即保存响应数据
    """
    # 构建请求数据
    payload = {
        "header": {
            "traceId": trace_id
        },
        "payload": {
            "ldocIdList": batch
        }
    }
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送POST请求
        response = requests.post(api_url, json=payload)
        response_data = response.json()
        
        # 计算耗时
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 处理响应
        results = []
        if response.status_code == 200 and "payload" in response_data and "data" in response_data["payload"]:
            results = response_data["payload"]["data"]
            
            # 立即将结果追加到文件
            with open(output_file, 'a', encoding='utf-8') as f:
                for result in results:
                    f.write(json.dumps(result, ensure_ascii=False) + "\n")

        else:
            print(f"API请求失败: {response.status_code}, {response.text[:200]}")
        
        return results
    
    except Exception as e:
        print(f"发送API请求时出错: {str(e)}")
        return []


def fetch_data_from_api(ldoc_ids: List[str], output_file: str, batch_size: int = 10, max_workers: int = MAX_WORKERS) -> int:
    """
    将ldoc_ids分批并发发送到API，并边请求边保存结果
    返回成功处理的记录数
    """
    api_url = "http://10.103.240.171:30750/datashard/api/v1/doc/fetch"
    timestamp = int(time.time())
    trace_id = f"test_fetch_{timestamp}"
    
    # 确保输出文件目录存在，并清空文件内容（如果已存在）
    ensure_output_directory(output_file)
    open(output_file, 'w').close()
    
    # 分批处理记录
    batches = []
    for i in range(0, len(ldoc_ids), batch_size):
        batches.append(ldoc_ids[i:i+batch_size])
    
    total_processed = 0
    start_time = time.time()
    
    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_batch = {
            executor.submit(fetch_and_save_batch, batch, trace_id, api_url, output_file): i 
            for i, batch in enumerate(batches)
        }
        
        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_batch):
            batch_index = future_to_batch[future]
            try:
                results = future.result()
                total_processed += len(results)
                # print(f"批次 {batch_index + 1}/{len(batches)} 已完成处理")
            except Exception as e:
                print(f"处理批次 {batch_index + 1} 时出错: {str(e)}")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"总共处理 {len(ldoc_ids)} 条记录，成功获取 {total_processed} 条结果")
    print(f"总耗时 {total_time:.4f} 秒，平均每条记录耗时 {total_time/len(ldoc_ids):.4f} 秒")
    
    return total_processed


def main():
    # 记录总体开始时间
    total_start_time = time.time()
    
    # 步骤1: 读取fetch_prop.txt文件，提取id列表
    fetch_prop_file = "fetch_file/fetch_prop.txt"
    ldoc_ids = read_ldoc_ids(fetch_prop_file)
    print(f"读取了 {len(ldoc_ids)} 个ldoc_id")
    
    if not ldoc_ids:
        print("没有找到ldoc_id，程序退出")
        return
    
    # 步骤2和3: 调用API获取数据并边请求边存储
    output_file = "fetch_file/fetch.txt"
    processed_count = fetch_data_from_api(ldoc_ids, output_file)
    
    print(f"已将 {processed_count} 条API响应保存到 {output_file}")
    
    # 计算总耗时
    total_end_time = time.time()
    total_elapsed_time = total_end_time - total_start_time
    print(f"程序总耗时: {total_elapsed_time:.4f} 秒")


if __name__ == "__main__":
    main()