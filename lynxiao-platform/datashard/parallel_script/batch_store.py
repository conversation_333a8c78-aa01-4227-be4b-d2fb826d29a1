"""
 <AUTHOR> 2025/4/28 14:55
"""

import os
import json
import time
import requests
import concurrent.futures
from typing import List, Dict, Any, Union

# 定义字符串长度阈值常量
MIN_STRING_LENGTH = 100

# 定义线程数，可配置
MAX_WORKERS = 16

# 定义用于记录字符串的类
class StringRecord:
    def __init__(self, ref_id: str, data: str):
        self.ref_id = ref_id
        self.data = data
    
    def to_dict(self) -> Dict[str, str]:
        return {
            "refId": self.ref_id,
            "data": self.data
        }
    
    def __str__(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)


def read_json_files(directory: str) -> List[Dict[str, Any]]:
    """
    读取指定目录下的所有JSON文件，并解析每行记录
    """
    records = []
    
    # 确保目录存在
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")
        return records
    
    # 遍历目录下的所有文件
    for filename in os.listdir(directory):
        if not filename.endswith('.json'):
            continue
        
        file_path = os.path.join(directory, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        record = json.loads(line)
                        records.append(record)
                    except json.JSONDecodeError:
                        print(f"无法解析JSON记录: {line[:100]}...")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
    
    return records


def extract_strings_recursive(data: Any, record_id: str, domain: str, path: str = "") -> List[StringRecord]:
    """
    递归遍历数据结构，提取所有长度大于MIN_STRING_LENGTH的字符串
    """
    results = []
    
    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            results.extend(extract_strings_recursive(value, record_id, domain, new_path))
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}[{i}]"
            results.extend(extract_strings_recursive(item, record_id, domain, new_path))
    
    elif isinstance(data, str) and len(data) > MIN_STRING_LENGTH:
        ref_id = f"{record_id}:{domain}:{path}"
        results.append(StringRecord(ref_id, data))
    
    return results


def save_string_records(records: List[StringRecord], output_file: str):
    """
    将字符串记录保存到文件
    """
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for record in records:
            f.write(json.dumps(record.to_dict(), ensure_ascii=False) + "\n")


def send_batch_to_api(batch: List[StringRecord], trace_id: str, api_url: str) -> List[Dict[str, Any]]:
    """
    发送一批记录到API，并返回响应结果
    """
    # 构建请求数据
    payload = {
        "header": {
            "traceId": trace_id
        },
        "payload": {
            "data": [record.to_dict() for record in batch]
        }
    }
    
    try:
        # 发送POST请求
        response = requests.post(api_url, json=payload)
        response_data = response.json()
        
        # 处理响应
        if response.status_code == 200 and "payload" in response_data and "data" in response_data["payload"]:
            return response_data["payload"]["data"]
        else:
            print(f"API请求失败: {response.status_code}, {response.text[:200]}")
            return []
    
    except Exception as e:
        print(f"发送API请求时出错: {str(e)}")
        return []


def send_to_api(records: List[StringRecord], batch_size: int = 10, max_workers: int = MAX_WORKERS):
    """
    将记录分批并发发送到API，并保存响应结果
    """
    api_url = "http://10.103.240.171:30750/datashard/api/v1/doc"
    timestamp = int(time.time())
    trace_id = f"test_datashard_{timestamp}"
    
    # 确保输出目录存在
    output_dir = os.path.dirname("fetch_file/fetch_prop.txt")
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 分批处理记录
    batches = []
    for i in range(0, len(records), batch_size):
        batches.append(records[i:i+batch_size])
    
    all_results = []
    start_time = time.time()
    
    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_batch = {
            executor.submit(send_batch_to_api, batch, trace_id, api_url): i 
            for i, batch in enumerate(batches)
        }
        
        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_batch):
            batch_index = future_to_batch[future]
            try:
                results = future.result()
                all_results.extend(results)
            except Exception as e:
                print(f"处理批次 {batch_index + 1} 时出错: {str(e)}")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"API调用总耗时: {total_time:.4f} 秒")
    print(f"平均每条记录耗时: {total_time/len(records):.4f} 秒")
    
    # 将所有结果写入文件
    with open("fetch_file/fetch_prop.txt", 'w', encoding='utf-8') as f:
        for item in all_results:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")


def main():
    # 记录总体开始时间
    total_start_time = time.time()
    
    # 步骤1: 读取JSON文件
    store_dir = "store_file"
    records = read_json_files(store_dir)
    print(f"读取了 {len(records)} 条记录")
    
    # 步骤2: 解析记录，提取字符串
    all_string_records = []
    for record in records:
        if "_id" in record and "domain" in record:
            record_id = str(record["_id"])
            domain = record["domain"]
            string_records = extract_strings_recursive(record, record_id, domain)
            all_string_records.extend(string_records)
    
    print(f"提取了 {len(all_string_records)} 条字符串记录")
    
    # 保存字符串记录到文件
    save_string_records(all_string_records, "store_file/store_prop.txt")
    print(f"已将字符串记录保存到 store_file/store_prop.txt")
    
    # 步骤3和4: 并发发送到API并保存响应
    send_to_api(all_string_records)
    print(f"已将API响应保存到 fetch_file/fetch_prop.txt")
    
    # 记录总体结束时间并计算总耗时
    total_end_time = time.time()
    total_elapsed_time = total_end_time - total_start_time
    print(f"程序总耗时: {total_elapsed_time:.4f} 秒")


if __name__ == "__main__":
    main()

