"""
 <AUTHOR> 2025/4/28 17:15
"""

import json
import os
import time
import requests
import concurrent.futures
from typing import List, Dict, Any, Tuple

# 配置常量
BATCH_SIZE = 10
MAX_WORKERS = 16


def read_doc_info(file_path: str) -> List[Tuple[str, str]]:
    """
    读取文件中的每一行，提取refId和id字段形成列表
    """
    doc_info_list = []
    
    # 确保文件存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return doc_info_list
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    record = json.loads(line)
                    if "id" in record and "refId" in record:
                        doc_info_list.append((record["refId"], record["id"]))
                except json.JSONDecodeError:
                    print(f"无法解析JSON记录: {line[:100]}...")
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
    
    return doc_info_list


def ensure_output_directory(output_file: str):
    """
    确保输出目录存在
    """
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    return output_file


def delete_batch_and_save(batch: List[Tuple[str, str]], trace_id: str, api_url: str, output_file: str) -> List[Dict[str, Any]]:
    """
    发送一批文档删除请求到API，并立即保存响应数据
    """
    # 构建请求数据
    docs = []
    for ref_id, ldoc_id in batch:
        docs.append({
            "ldocId": ldoc_id,
            "refId": ref_id
        })
    
    payload = {
        "header": {
            "traceId": trace_id
        },
        "payload": {
            "docs": docs
        }
    }
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送DELETE请求
        response = requests.delete(api_url, json=payload)
        response_data = response.json()
        
        # 计算耗时
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 处理响应
        results = []
        if response.status_code == 200 and "payload" in response_data:
            results = response_data["payload"].get("data", [])
            
            # 立即将结果追加到文件
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(response_data, ensure_ascii=False) + "\n")
            
            print(f"批次处理完成，删除了 {len(batch)} 条记录，耗时 {elapsed_time:.4f} 秒")
        else:
            print(f"API请求失败: {response.status_code}, {response.text[:200]}")
            # 即使失败也记录响应
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(response_data, ensure_ascii=False) + "\n")
        
        return results
    
    except Exception as e:
        error_msg = f"发送API请求时出错: {str(e)}"
        print(error_msg)
        
        # 记录错误信息
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps({"error": error_msg}, ensure_ascii=False) + "\n")
        
        return []


def delete_docs(doc_info_list: List[Tuple[str, str]], output_file: str, batch_size: int = BATCH_SIZE, max_workers: int = MAX_WORKERS) -> int:
    """
    将文档信息分批并发发送到API进行删除，并边请求边保存结果
    返回成功处理的记录数
    """
    api_url = "http://10.103.240.171:30750/datashard/api/v1/doc"
    timestamp = int(time.time())
    trace_id = f"test_delete_{timestamp}"
    
    # 确保输出文件目录存在，并清空文件内容（如果已存在）
    ensure_output_directory(output_file)
    open(output_file, 'w').close()
    
    # 分批处理记录
    batches = []
    for i in range(0, len(doc_info_list), batch_size):
        batches.append(doc_info_list[i:i+batch_size])
    
    total_processed = 0
    start_time = time.time()
    
    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_batch = {
            executor.submit(delete_batch_and_save, batch, trace_id, api_url, output_file): i 
            for i, batch in enumerate(batches)
        }
        
        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_batch):
            batch_index = future_to_batch[future]
            try:
                results = future.result()
                total_processed += len(results)
                print(f"批次 {batch_index + 1}/{len(batches)} 已完成处理")
            except Exception as e:
                print(f"处理批次 {batch_index + 1} 时出错: {str(e)}")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"总共处理 {len(doc_info_list)} 条记录，成功删除 {total_processed} 条")
    print(f"总耗时 {total_time:.4f} 秒，平均每条记录耗时 {total_time/len(doc_info_list):.4f} 秒")
    
    return total_processed


def main():
    # 记录总体开始时间
    total_start_time = time.time()
    
    # 步骤1: 读取fetch_prop.txt文件，提取refId和id列表
    fetch_prop_file = "fetch_file/fetch_prop.txt"
    doc_info_list = read_doc_info(fetch_prop_file)
    print(f"读取了 {len(doc_info_list)} 条文档信息")
    
    if not doc_info_list:
        print("没有找到文档信息，程序退出")
        return
    
    # 步骤2和3: 调用API删除文档并边请求边存储结果
    output_file = "delete_file/delete.txt"
    processed_count = delete_docs(doc_info_list, output_file)
    
    print(f"已将 {processed_count} 条API响应保存到 {output_file}")
    
    # 计算总耗时
    total_end_time = time.time()
    total_elapsed_time = total_end_time - total_start_time
    print(f"程序总耗时: {total_elapsed_time:.4f} 秒")


if __name__ == "__main__":
    main()