"""
 <AUTHOR> 2025/8/26 19:45
"""

"""
集成测试脚本 - 正文缓存服务性能测试
该脚本集成了batch_store.py、batch_fetch.py和batch_delete.py三个脚本的功能
按照 store -> fetch -> delete 的顺序进行完整的测试轮次

<AUTHOR> 2025/8/26
"""

import concurrent.futures
import configparser
import json
import os
import sys
import threading
import time
from typing import List, Dict, Any, Tuple

import requests

# 添加父目录到路径，以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 定义字符串长度阈值常量
MIN_STRING_LENGTH = 100


class IntegratedDatashardTester:
    """集成数据分片测试器"""

    def __init__(self, config_file: str = "config.ini"):
        """初始化测试器"""
        self.config = configparser.ConfigParser()
        self.config.read(config_file)

        # 解析配置
        self.nodes = [node.strip() for node in self.config.get('cluster', 'nodes').split(',')]
        self.threads_per_node = self.config.getint('threads', 'threads_per_node')
        self.batch_size = self.config.getint('batch', 'batch_size')

        # 测试控制配置
        self.test_mode = self.config.get('test_control', 'test_mode')
        self.max_rounds = self.config.getint('test_control', 'max_rounds') if self.test_mode == 'rounds' else None
        self.max_time_seconds = self.config.getint('test_control',
                                                   'max_time_seconds') if self.test_mode == 'time' else None

        # 数据配置
        self.store_dir = self.config.get('data', 'store_dir')
        self.output_dir = self.config.get('data', 'output_dir')

        # API路径配置
        self.store_path = self.config.get('api', 'store_path')
        self.fetch_path = self.config.get('api', 'fetch_path')
        self.delete_path = self.config.get('api', 'delete_path')

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 线程锁
        self.file_lock = threading.Lock()

        print(f"初始化完成:")
        print(f"  集群节点: {self.nodes}")
        print(f"  每节点线程数: {self.threads_per_node}")
        print(f"  批处理大小: {self.batch_size}")
        print(f"  测试模式: {self.test_mode}")
        if self.test_mode == 'rounds':
            print(f"  最大轮数: {self.max_rounds}")
        else:
            print(f"  最大时间: {self.max_time_seconds}秒")

    def send_batch_to_node(self, node_url: str, api_path: str, payload: Dict[str, Any], method: str = 'POST') -> Dict[str, Any]:
        """向指定节点发送请求"""
        full_url = f"{node_url}{api_path}"

        try:
            if method.upper() == 'POST':
                response = requests.post(full_url, json=payload, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(full_url, json=payload, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            if response.status_code == 200:
                return response.json()
            else:
                print(f"API请求失败: {response.status_code}, {response.text[:200]}")
                return {}
        except Exception as e:
            print(f"发送请求到 {full_url} 时出错: {str(e)}")
            return {}


class StringRecord:
    """字符串记录类"""

    def __init__(self, ref_id: str, data: str):
        self.ref_id = ref_id
        self.data = data

    def to_dict(self) -> Dict[str, str]:
        return {
            "refId": self.ref_id,
            "data": self.data
        }

    def __str__(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)


def read_json_files(directory: str) -> List[Dict[str, Any]]:
    """读取指定目录下的所有JSON文件，并解析每行记录"""
    records = []

    if not os.path.exists(directory):
        print(f"目录不存在: {directory}")
        return records

    for filename in os.listdir(directory):
        if not filename.endswith('.json'):
            continue

        file_path = os.path.join(directory, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        record = json.loads(line)
                        records.append(record)
                    except json.JSONDecodeError:
                        print(f"无法解析JSON记录: {line[:100]}...")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")

    return records


def extract_strings_recursive(data: Any, record_id: str, domain: str, path: str = "") -> List[StringRecord]:
    """递归遍历数据结构，提取所有长度大于MIN_STRING_LENGTH的字符串"""
    results = []

    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            results.extend(extract_strings_recursive(value, record_id, domain, new_path))

    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}[{i}]"
            results.extend(extract_strings_recursive(item, record_id, domain, new_path))

    elif isinstance(data, str) and len(data) > MIN_STRING_LENGTH:
        ref_id = f"{record_id}:{domain}:{path}"
        results.append(StringRecord(ref_id, data))

    return results


def ensure_directory(file_path: str):
    """确保目录存在"""
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        """并行存储数据到多个节点"""
        timestamp = int(time.time())
        trace_id = f"integrated_test_store_round_{round_num}_{timestamp}"

        # 将数据分配到各个节点
        node_batches = []
        records_per_node = len(string_records) // len(self.nodes)

        for i, node in enumerate(self.nodes):
            start_idx = i * records_per_node
            end_idx = start_idx + records_per_node if i < len(self.nodes) - 1 else len(string_records)
            node_records = string_records[start_idx:end_idx]

            # 将节点数据进一步分批
            for j in range(0, len(node_records), self.batch_size):
                batch = node_records[j:j + self.batch_size]
                node_batches.append((node, batch))

        all_results = []

        # 使用线程池并行处理所有节点的所有批次
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.nodes) * self.threads_per_node) as executor:
            futures = []

            for node, batch in node_batches:
                payload = {
                    "header": {"traceId": trace_id},
                    "payload": {"data": [record.to_dict() for record in batch]}
                }
                future = executor.submit(self.send_batch_to_node, node, self.store_path, payload, 'POST')
                futures.append(future)

            # 收集结果
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result and "payload" in result and "data" in result["payload"]:
                        all_results.extend(result["payload"]["data"])
                except Exception as e:
                    print(f"处理store批次时出错: {str(e)}")

        return all_results

    def fetch_batch_parallel(self, ldoc_ids: List[str], round_num: int) -> List[Dict[str, Any]]:
        """并行从多个节点获取数据"""
        timestamp = int(time.time())
        trace_id = f"integrated_test_fetch_round_{round_num}_{timestamp}"

        # 将ID分配到各个节点
        node_batches = []
        ids_per_node = len(ldoc_ids) // len(self.nodes)

        for i, node in enumerate(self.nodes):
            start_idx = i * ids_per_node
            end_idx = start_idx + ids_per_node if i < len(self.nodes) - 1 else len(ldoc_ids)
            node_ids = ldoc_ids[start_idx:end_idx]

            # 将节点数据进一步分批
            for j in range(0, len(node_ids), self.batch_size):
                batch = node_ids[j:j + self.batch_size]
                node_batches.append((node, batch))

        all_results = []

        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.nodes) * self.threads_per_node) as executor:
            futures = []

            for node, batch in node_batches:
                payload = {
                    "header": {"traceId": trace_id},
                    "payload": {"ldocIdList": batch}
                }
                future = executor.submit(self.send_batch_to_node, node, self.fetch_path, payload, 'POST')
                futures.append(future)

            # 收集结果
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result and "payload" in result and "data" in result["payload"]:
                        all_results.extend(result["payload"]["data"])
                except Exception as e:
                    print(f"处理fetch批次时出错: {str(e)}")

        return all_results

    def delete_batch_parallel(self, doc_info_list: List[Tuple[str, str]], round_num: int) -> List[Dict[str, Any]]:
        """并行从多个节点删除数据"""
        timestamp = int(time.time())
        trace_id = f"integrated_test_delete_round_{round_num}_{timestamp}"

        # 将文档信息分配到各个节点
        node_batches = []
        docs_per_node = len(doc_info_list) // len(self.nodes)

        for i, node in enumerate(self.nodes):
            start_idx = i * docs_per_node
            end_idx = start_idx + docs_per_node if i < len(self.nodes) - 1 else len(doc_info_list)
            node_docs = doc_info_list[start_idx:end_idx]

            # 将节点数据进一步分批
            for j in range(0, len(node_docs), self.batch_size):
                batch = node_docs[j:j + self.batch_size]
                node_batches.append((node, batch))

        all_results = []

        # 使用线程池并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=len(self.nodes) * self.threads_per_node) as executor:
            futures = []

            for node, batch in node_batches:
                docs = []
                for ref_id, ldoc_id in batch:
                    docs.append({"ldocId": ldoc_id, "refId": ref_id})

                payload = {
                    "header": {"traceId": trace_id},
                    "payload": {"docs": docs}
                }
                future = executor.submit(self.send_batch_to_node, node, self.delete_path, payload, 'DELETE')
                futures.append(future)

            # 收集结果
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result and "payload" in result:
                        all_results.append(result)
                except Exception as e:
                    print(f"处理delete批次时出错: {str(e)}")

        return all_results

    def save_results_to_file(self, data: List[Dict[str, Any]], file_path: str):
        """保存结果到文件"""
        ensure_directory(file_path)

        with self.file_lock:
            with open(file_path, 'w', encoding='utf-8') as f:
                for item in data:
                    f.write(json.dumps(item, ensure_ascii=False) + "\n")

    def read_ldoc_ids_from_file(self, file_path: str) -> List[str]:
        """从文件中读取ldoc_ids"""
        ldoc_ids = []

        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return ldoc_ids

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        record = json.loads(line)
                        if "id" in record:
                            ldoc_ids.append(record["id"])
                    except json.JSONDecodeError:
                        print(f"无法解析JSON记录: {line[:100]}...")
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {str(e)}")

        return ldoc_ids

    def read_doc_info_from_file(self, file_path: str) -> List[Tuple[str, str]]:
        """从文件中读取文档信息(refId, id)"""
        doc_info_list = []

        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return doc_info_list

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        record = json.loads(line)
                        if "id" in record and "refId" in record:
                            doc_info_list.append((record["refId"], record["id"]))
                    except json.JSONDecodeError:
                        print(f"无法解析JSON记录: {line[:100]}...")
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {str(e)}")

        return doc_info_list

    def run_single_round(self, round_num: int) -> bool:
        """执行单轮完整测试：store -> fetch -> delete"""
        print(f"\n=== 开始第 {round_num} 轮测试 ===")
        round_start_time = time.time()

        try:
            # 步骤1: 读取原始数据并提取字符串
            print(f"[轮次 {round_num}] 步骤1: 读取和处理原始数据...")
            records = read_json_files(self.store_dir)
            if not records:
                print(f"[轮次 {round_num}] 错误: 没有找到原始数据")
                return False

            all_string_records = []
            for record in records:
                if "_id" in record and "domain" in record:
                    record_id = str(record["_id"])
                    domain = record["domain"]
                    string_records = extract_strings_recursive(record, record_id, domain)
                    all_string_records.extend(string_records)

            print(f"[轮次 {round_num}] 提取了 {len(all_string_records)} 条字符串记录")

            # 步骤2: Store - 存储数据
            print(f"[轮次 {round_num}] 步骤2: 存储数据到集群...")
            store_start_time = time.time()
            store_results = self.store_batch_parallel(all_string_records, round_num)
            store_end_time = time.time()

            if not store_results:
                print(f"[轮次 {round_num}] 错误: Store操作失败")
                return False

            print(
                f"[轮次 {round_num}] Store完成: {len(store_results)} 条记录，耗时 {store_end_time - store_start_time:.2f}秒")

            # 保存store结果
            store_result_file = os.path.join(self.output_dir, f"store_results_round_{round_num}.txt")
            self.save_results_to_file(store_results, store_result_file)

            # 步骤3: Fetch - 获取数据
            print(f"[轮次 {round_num}] 步骤3: 从集群获取数据...")
            ldoc_ids = [result["id"] for result in store_results if "id" in result]

            if not ldoc_ids:
                print(f"[轮次 {round_num}] 错误: 没有获取到有效的ldoc_ids")
                return False

            fetch_start_time = time.time()
            fetch_results = self.fetch_batch_parallel(ldoc_ids, round_num)
            fetch_end_time = time.time()

            print(
                f"[轮次 {round_num}] Fetch完成: {len(fetch_results)} 条记录，耗时 {fetch_end_time - fetch_start_time:.2f}秒")

            # 保存fetch结果
            fetch_result_file = os.path.join(self.output_dir, f"fetch_results_round_{round_num}.txt")
            self.save_results_to_file(fetch_results, fetch_result_file)

            # 步骤4: Delete - 删除数据
            print(f"[轮次 {round_num}] 步骤4: 从集群删除数据...")
            doc_info_list = [(result["refId"], result["id"]) for result in store_results
                             if "refId" in result and "id" in result]

            if not doc_info_list:
                print(f"[轮次 {round_num}] 错误: 没有获取到有效的文档信息用于删除")
                return False

            delete_start_time = time.time()
            delete_results = self.delete_batch_parallel(doc_info_list, round_num)
            delete_end_time = time.time()

            print(
                f"[轮次 {round_num}] Delete完成: {len(delete_results)} 条记录，耗时 {delete_end_time - delete_start_time:.2f}秒")

            # 保存delete结果
            delete_result_file = os.path.join(self.output_dir, f"delete_results_round_{round_num}.txt")
            self.save_results_to_file(delete_results, delete_result_file)

            round_end_time = time.time()
            total_round_time = round_end_time - round_start_time

            print(f"[轮次 {round_num}] 完成! 总耗时: {total_round_time:.2f}秒")
            print(f"[轮次 {round_num}] 性能统计:")
            print(f"  - Store: {len(store_results)} 条记录，{store_end_time - store_start_time:.2f}秒")
            print(f"  - Fetch: {len(fetch_results)} 条记录，{fetch_end_time - fetch_start_time:.2f}秒")
            print(f"  - Delete: {len(delete_results)} 条记录，{delete_end_time - delete_start_time:.2f}秒")

            return True

        except Exception as e:
            print(f"[轮次 {round_num}] 执行过程中出错: {str(e)}")
            return False

    def run_test(self):
        """运行完整的集成测试"""
        print("=" * 60)
        print("开始集成测试 - 正文缓存服务性能测试")
        print("=" * 60)

        test_start_time = time.time()
        completed_rounds = 0

        try:
            if self.test_mode == 'rounds':
                # 按轮数控制
                print(f"测试模式: 按轮数控制，最大轮数: {self.max_rounds}")

                for round_num in range(1, self.max_rounds + 1):
                    success = self.run_single_round(round_num)
                    if success:
                        completed_rounds += 1
                    else:
                        print(f"轮次 {round_num} 失败，继续下一轮...")

            else:
                # 按时间控制
                print(f"测试模式: 按时间控制，最大时间: {self.max_time_seconds}秒")

                round_num = 1
                while True:
                    current_time = time.time()
                    elapsed_time = current_time - test_start_time

                    # 检查是否超时，但要确保完成当前轮次
                    if elapsed_time >= self.max_time_seconds:
                        print(f"已达到最大测试时间 {self.max_time_seconds}秒，开始执行最后一轮...")
                        success = self.run_single_round(round_num)
                        if success:
                            completed_rounds += 1
                        break

                    success = self.run_single_round(round_num)
                    if success:
                        completed_rounds += 1
                    else:
                        print(f"轮次 {round_num} 失败，继续下一轮...")

                    round_num += 1

        except KeyboardInterrupt:
            print("\n收到中断信号，正在安全退出...")
        except Exception as e:
            print(f"测试过程中出现异常: {str(e)}")

        finally:
            test_end_time = time.time()
            total_test_time = test_end_time - test_start_time

            print("\n" + "=" * 60)
            print("集成测试完成")
            print("=" * 60)
            print(f"总测试时间: {total_test_time:.2f}秒")
            print(f"完成轮数: {completed_rounds}")
            if completed_rounds > 0:
                print(f"平均每轮耗时: {total_test_time / completed_rounds:.2f}秒")
            print(f"测试结果文件保存在: {self.output_dir}")


def main():
    """主函数"""
    try:
        # 检查配置文件是否存在
        config_file = "config.ini"
        if not os.path.exists(config_file):
            print(f"错误: 配置文件 {config_file} 不存在")
            print("请确保配置文件存在并包含正确的配置信息")
            return

        # 创建测试器实例
        tester = IntegratedDatashardTester(config_file)

        # 运行测试
        tester.run_test()

    except Exception as e:
        print(f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
