[cluster]
# 集群节点配置，多个节点用逗号分隔
nodes = http://10.103.240.171:30750

[threads]
# 每个节点的线程数配置
threads_per_node = 16

[batch]
# 批处理大小
batch_size = 10

[test_control]
# 测试控制方式：rounds（按轮数）或 time（按时间，单位秒）
# 如果两个都配置，优先使用 rounds
test_mode = rounds
max_rounds = 1
max_time_seconds = 3600

[data]
# 数据文件配置
store_dir = store_file
output_dir = all_test

[api]
# API路径配置
store_path = /datashard/api/v1/doc
fetch_path = /datashard/api/v1/doc/fetch
delete_path = /datashard/api/v1/doc