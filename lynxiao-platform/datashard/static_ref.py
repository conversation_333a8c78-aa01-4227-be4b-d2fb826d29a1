import concurrent.futures
import threading
import time
import os

from pymongo import MongoClient

# 用于存储每个线程的结果
thread_local_results = threading.local()


def find_max_ref_in_collection(client_uri, db_name, collection_name):
    """
    在单个集合中查找ref字段长度最长的文档
    
    Args:
        client_uri: MongoDB连接URI
        db_name: 数据库名称
        collection_name: 集合名称
    
    Returns:
        包含集合名称、最大ref长度和对应文档ID的字典
    """
    try:
        # 为每个线程创建独立的连接
        client = MongoClient(client_uri)
        db = client[db_name]
        collection = db[collection_name]

        # 查找ref字段长度最长的文档
        max_ref_length = 0
        max_ref_doc_id = None

        # 使用聚合管道优化查询
        pipeline = [
            {"$addFields": {"refCount": {"$size": "$ref"}}},
            {"$sort": {"refCount": -1}},
            {"$limit": 1},
            {"$project": {"id": "$_id", "refCount": 1, "_id": 0}}
        ]

        result = list(collection.aggregate(pipeline))

        if result:
            max_ref_doc = result[0]
        max_ref_length = max_ref_doc.get("refCount", 0)
        max_ref_doc_id = max_ref_doc.get("id")

        return {
            "collection_name": collection_name,
            "max_ref_length": max_ref_length,
            "max_ref_doc_id": max_ref_doc_id
        }

    except Exception as e:
        print(f"处理集合 {collection_name} 时出错: {e}")
        return {
            "collection_name": collection_name,
            "error": str(e)
        }
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


def find_max_ref_documents_parallel(mongo_uri, database_name, max_workers=10, output_file=None):
    """
    并发遍历MongoDB数据库中的所有集合，找出每个集合中ref字段长度最长的文档
    
    Args:
        mongo_uri: MongoDB连接URI
        database_name: 要处理的数据库名称
        max_workers: 最大并发工作线程数
        output_file: 输出文件对象，如果提供则将结果写入文件
    """
    try:
        # 连接到MongoDB获取集合列表
        client = MongoClient(mongo_uri)
        db = client[database_name]

        # 获取数据库中的所有集合
        collections = db.list_collection_names()

        if not collections:
            message = f"数据库 '{database_name}' 中没有找到任何集合"
            print(message)
            if output_file:
                output_file.write(message + "\n")
            return

        message = f"开始并发处理数据库 '{database_name}' 中的 {len(collections)} 个集合..."
        print(message)
        if output_file:
            output_file.write(message + "\n")

        # 使用线程池并发处理每个集合
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_collection = {
                executor.submit(find_max_ref_in_collection, mongo_uri, database_name, collection_name): collection_name
                for collection_name in collections
            }

            # 处理结果
            for future in concurrent.futures.as_completed(future_to_collection):
                result = future.result()
                if "error" in result:
                    message = f"集合: {result['collection_name']}, 处理出错: {result['error']}"
                    print(message)
                    if output_file:
                        output_file.write(message + "\n")
                elif result["max_ref_doc_id"]:
                    message = f"集合: {result['collection_name']}, 最长ref长度: {result['max_ref_length']}, 文档ID: {result['max_ref_doc_id']}"
                    print(message)
                    if output_file:
                        output_file.write(message + "\n")
                else:
                    message = f"集合: {result['collection_name']}, 没有找到包含ref字段的文档"
                    print(message)
                    if output_file:
                        output_file.write(message + "\n")

    except Exception as e:
        message = f"处理过程中出错: {e}"
        print(message)
        if output_file:
            output_file.write(message + "\n")
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


def get_collection_stats_for_single(client_uri, db_name, collection_name):
    """
    获取单个集合的统计信息
    
    Args:
        client_uri: MongoDB连接URI
        db_name: 数据库名称
        collection_name: 集合名称
    
    Returns:
        包含集合统计信息的字典
    """
    try:
        # 为每个线程创建独立的连接
        client = MongoClient(client_uri)
        db = client[db_name]

        # 获取集合统计信息
        stats = db.command("collStats", collection_name)

        # 获取集合大小（MB）
        size_mb = stats.get("size", 0) / (1024 * 1024)
        # 获取索引大小（MB）
        index_size_mb = stats.get("totalIndexSize", 0) / (1024 * 1024)
        # 获取文档数量
        count = stats.get("count", 0)

        return {
            "collection_name": collection_name,
            "count": count,
            "size_mb": size_mb,
            "index_size_mb": index_size_mb
        }

    except Exception as e:
        print(f"获取集合 {collection_name} 统计信息时出错: {e}")
        return {
            "collection_name": collection_name,
            "error": str(e)
        }
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


def get_collection_stats_parallel(mongo_uri, database_name, max_workers=10, output_file=None):
    """
    并发统计MongoDB数据库中每个集合所占内存大小
    
    Args:
        mongo_uri: MongoDB连接URI
        database_name: 要处理的数据库名称
        max_workers: 最大并发工作线程数
        output_file: 输出文件对象，如果提供则将结果写入文件
    """
    try:
        # 连接到MongoDB获取集合列表
        client = MongoClient(mongo_uri)
        db = client[database_name]

        # 获取数据库中的所有集合
        collections = db.list_collection_names()

        if not collections:
            message = f"数据库 '{database_name}' 中没有找到任何集合"
            print(message)
            if output_file:
                output_file.write(message + "\n")
            return

        header = f"\n数据库 '{database_name}' 中各集合的内存占用情况:"
        print(header)
        if output_file:
            output_file.write(header + "\n")
        
        separator = "-" * 60
        print(separator)
        if output_file:
            output_file.write(separator + "\n")
        
        table_header = f"{'集合名称':<30} {'文档数量':<10} {'存储大小(MB)':<15} {'索引大小(MB)':<15}"
        print(table_header)
        if output_file:
            output_file.write(table_header + "\n")
        
        print(separator)
        if output_file:
            output_file.write(separator + "\n")

        total_size = 0
        total_index_size = 0
        total_docs = 0

        # 使用线程池并发获取每个集合的统计信息
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_collection = {
                executor.submit(get_collection_stats_for_single, mongo_uri, database_name,
                                collection_name): collection_name
                for collection_name in collections
            }

            # 收集所有结果
            results = []
            for future in concurrent.futures.as_completed(future_to_collection):
                result = future.result()
                if "error" not in result:
                    results.append(result)

                    # 累计总大小
                    total_size += result["size_mb"]
                    total_index_size += result["index_size_mb"]
                    total_docs += result["count"]
                else:
                    message = f"获取集合 {result['collection_name']} 统计信息时出错: {result['error']}"
                    print(message)
                    if output_file:
                        output_file.write(message + "\n")

            # 按集合名称排序并打印结果
            for result in sorted(results, key=lambda x: x["collection_name"]):
                row = f"{result['collection_name']:<30} {result['count']:<10} {result['size_mb']:<15.2f} {result['index_size_mb']:<15.2f}"
                print(row)
                if output_file:
                    output_file.write(row + "\n")

        print(separator)
        if output_file:
            output_file.write(separator + "\n")
        
        total_row = f"{'总计':<30} {total_docs:<10} {total_size:<15.2f} {total_index_size:<15.2f}"
        print(total_row)
        if output_file:
            output_file.write(total_row + "\n")
        
        summary = f"总存储大小(含索引): {total_size + total_index_size:.2f} MB"
        print(summary)
        if output_file:
            output_file.write(summary + "\n")

    except Exception as e:
        message = f"获取集合统计信息时出错: {e}"
        print(message)
        if output_file:
            output_file.write(message + "\n")
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


def get_ref_stats_for_collection(client_uri, db_name, collection_name):
    """
    获取单个集合中ref字段的统计信息（平均值、中位数、最大值、最小值）
    
    Args:
        client_uri: MongoDB连接URI
        db_name: 数据库名称
        collection_name: 集合名称
    
    Returns:
        包含ref字段统计信息的字典
    """
    try:
        # 为每个线程创建独立的连接
        client = MongoClient(client_uri)
        db = client[db_name]
        collection = db[collection_name]
        
        # 检查集合中是否有文档
        doc_count = collection.count_documents({})
        if doc_count == 0:
            return {
                "collection_name": collection_name,
                "has_ref": False,
                "error": "集合为空"
            }
            
        # 检查是否有包含ref字段的文档
        ref_doc_count = collection.count_documents({"ref": {"$exists": True}})
        if ref_doc_count == 0:
            return {
                "collection_name": collection_name,
                "has_ref": False,
                "error": "没有包含ref字段的文档"
            }
            
        # 使用聚合管道计算统计信息，但不使用$push收集所有值
        pipeline = [
            {"$match": {"ref": {"$exists": True}}},
            {"$addFields": {"refLength": {"$size": "$ref"}}},
            {"$group": {
                "_id": None,
                "avg": {"$avg": "$refLength"},
                "min": {"$min": "$refLength"},
                "max": {"$max": "$refLength"},
                "count": {"$sum": 1}
            }},
            {"$project": {
                "_id": 0,
                "avg": 1,
                "min": 1,
                "max": 1,
                "count": 1
            }}
        ]
        
        result = list(collection.aggregate(pipeline))
        
        if not result:
            return {
                "collection_name": collection_name,
                "has_ref": False,
                "error": "聚合计算失败"
            }
            
        stats = result[0]
        
        # 计算中位数 - 使用近似中位数方法
        # 对于大型集合，我们可以使用近似中位数或者分批计算
        # 这里使用一个简单的近似方法：取一个样本计算中位数
        sample_size = min(1000, ref_doc_count)  # 限制样本大小
        
        # 获取样本数据
        sample_pipeline = [
            {"$match": {"ref": {"$exists": True}}},
            {"$addFields": {"refLength": {"$size": "$ref"}}},
            {"$sample": {"size": sample_size}},
            {"$project": {"refLength": 1, "_id": 0}}
        ]
        
        sample_results = list(collection.aggregate(sample_pipeline))
        
        if sample_results:
            # 从样本中计算中位数
            ref_lengths = sorted([doc["refLength"] for doc in sample_results])
            n = len(ref_lengths)
            if n % 2 == 0:
                median = (ref_lengths[n//2 - 1] + ref_lengths[n//2]) / 2
            else:
                median = ref_lengths[n//2]
        else:
            median = stats["avg"]  # 如果无法获取样本，使用平均值作为近似
            
        # 查找最大ref长度对应的文档ID
        max_ref_doc = collection.find_one(
            {"ref": {"$exists": True}, "$expr": {"$eq": [{"$size": "$ref"}, stats["max"]]}},
            {"_id": 1}
        )
        
        max_ref_doc_id = max_ref_doc["_id"] if max_ref_doc else None
        
        return {
            "collection_name": collection_name,
            "has_ref": True,
            "count": stats["count"],
            "avg": stats["avg"],
            "median": median,
            "min": stats["min"],
            "max": stats["max"],
            "max_ref_doc_id": max_ref_doc_id
        }
        
    except Exception as e:
        print(f"处理集合 {collection_name} 的ref统计信息时出错: {e}")
        return {
            "collection_name": collection_name,
            "has_ref": False,
            "error": str(e)
        }
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


def get_ref_stats_parallel(mongo_uri, database_name, max_workers=10, output_file=None):
    """
    并发统计MongoDB数据库中每个集合的ref字段长度统计信息
    
    Args:
        mongo_uri: MongoDB连接URI
        database_name: 要处理的数据库名称
        max_workers: 最大并发工作线程数
        output_file: 输出文件对象，如果提供则将结果写入文件
    
    Returns:
        全局最大ref长度及其所在集合和文档ID
    """
    try:
        # 连接到MongoDB获取集合列表
        client = MongoClient(mongo_uri)
        db = client[database_name]

        # 获取数据库中的所有集合
        collections = db.list_collection_names()

        if not collections:
            message = f"数据库 '{database_name}' 中没有找到任何集合"
            print(message)
            if output_file:
                output_file.write(message + "\n")
            return None

        header = f"\n数据库 '{database_name}' 中各集合的ref字段长度统计:"
        print(header)
        if output_file:
            output_file.write(header + "\n")
        
        separator = "-" * 80
        print(separator)
        if output_file:
            output_file.write(separator + "\n")
        
        table_header = f"{'集合名称':<30} {'文档数量':<10} {'平均长度':<10} {'中位数':<10} {'最小值':<10} {'最大值':<10}"
        print(table_header)
        if output_file:
            output_file.write(table_header + "\n")
        
        print(separator)
        if output_file:
            output_file.write(separator + "\n")

        # 使用线程池并发获取每个集合的ref统计信息
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_collection = {
                executor.submit(get_ref_stats_for_collection, mongo_uri, database_name, collection_name): collection_name
                for collection_name in collections
            }

            # 收集所有结果
            results = []
            for future in concurrent.futures.as_completed(future_to_collection):
                result = future.result()
                if result["has_ref"]:
                    results.append(result)
                elif "error" in result:
                    message = f"集合: {result['collection_name']}, 处理出错: {result['error']}"
                    print(message)
                    if output_file:
                        output_file.write(message + "\n")

            # 按集合名称排序并打印结果
            for result in sorted(results, key=lambda x: x["collection_name"]):
                row = f"{result['collection_name']:<30} {result['count']:<10} {result['avg']:<10.2f} {result['median']:<10.2f} {result['min']:<10} {result['max']:<10}"
                print(row)
                if output_file:
                    output_file.write(row + "\n")

        print(separator)
        if output_file:
            output_file.write(separator + "\n")
        
        # 找出全局最大ref长度
        if results:
            global_max = max(results, key=lambda x: x["max"])
            max_summary = f"全局最大ref长度: {global_max['max']}, 集合: {global_max['collection_name']}, 文档ID: {global_max['max_ref_doc_id']}"
            print(max_summary)
            if output_file:
                output_file.write(max_summary + "\n")
            return global_max
        else:
            no_ref_message = "没有找到包含ref字段的集合"
            print(no_ref_message)
            if output_file:
                output_file.write(no_ref_message + "\n")
            return None

    except Exception as e:
        message = f"获取ref统计信息时出错: {e}"
        print(message)
        if output_file:
            output_file.write(message + "\n")
        return None
    finally:
        # 关闭连接
        if 'client' in locals():
            client.close()


if __name__ == "__main__":
    # 记录开始时间
    start_time = time.time()

    mongo_uri = "mongodb+srv://u_lynxiao:<EMAIL>/lynxiao_datashard?authSource=admin&tls=false&ssl=false"
    database_name = "lynxiao_datashard"

    # 设置最大并发线程数（可根据服务器性能调整）
    max_workers = 32

    # 将结果写到当前路径下的static_ref.txt文件中
    output_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "static_ref.txt")
    with open(output_file_path, "w", encoding="utf-8") as output_file:
        # 写入标题和时间戳
        output_file.write(f"MongoDB数据库 '{database_name}' 统计报告\n")
        output_file.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        output_file.write("-" * 60 + "\n\n")
        
        # 统计每个表里记录ref长度的平均数，中位数，最大值，最小值
        global_max_ref = get_ref_stats_parallel(mongo_uri, database_name, max_workers, output_file)
        
        # 如果找到了全局最大ref，添加一个总结部分
        if global_max_ref:
            output_file.write("\n" + "-" * 60 + "\n")
            output_file.write("全局最大ref长度总结:\n")
            output_file.write(f"最大ref长度: {global_max_ref['max']}\n")
            output_file.write(f"所在集合: {global_max_ref['collection_name']}\n")
            output_file.write(f"文档ID: {global_max_ref['max_ref_doc_id']}\n")
            output_file.write("-" * 60 + "\n")

        # 并发执行查找最长ref字段的文档
        # find_max_ref_documents_parallel(mongo_uri, database_name, max_workers, output_file)

        # 计算并打印执行耗时
        end_time = time.time()
        execution_time = end_time - start_time
        time_message = f"\n程序执行总耗时: {execution_time:.2f} 秒"
        print(time_message)
        output_file.write(time_message + "\n")
        
        print(f"\n结果已保存到文件: {output_file_path}")
