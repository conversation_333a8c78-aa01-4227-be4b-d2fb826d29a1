"""
 读取 MongoDB 数据并发送到 Kafka 的脚本

 <AUTHOR> 2025/6/26 16:38
"""

import json
import sys

from kafka import KafkaProducer
from pymongo import MongoClient


def convert_bytes(obj):
    if isinstance(obj, dict):
        return {k: convert_bytes(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_bytes(i) for i in obj]
    elif isinstance(obj, bytes):
        try:
            return obj.decode('utf-8')
        except Exception:
            import base64
            return base64.b64encode(obj).decode('utf-8')
    else:
        return obj


def build_kafka_message(data_list, trace_id="test_bbding_decode", node_code="a1440fdc0b7", bucket_code="bbding",
                        audit_type=1, is_sync=True):
    return {
        "header": {
            "traceId": trace_id
        },
        "parameter": {
            "current.workflow.nodeCode": node_code
        },
        "payload": {
            "bucketCode": bucket_code,
            "data": [data_list],
            "auditType": audit_type,
            "isSync": is_sync
        }
    }


def mongo_to_kafka(mongo_uri, db_name, collection_name, kafka_servers, kafka_topic, batch_size=100, limit=1000):
    client = MongoClient(mongo_uri)
    db = client[db_name]
    collection = db[collection_name]
    producer = KafkaProducer(
        bootstrap_servers=kafka_servers,
        value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
    )
    count = 0
    cursor = collection.find({}, no_cursor_timeout=True).batch_size(batch_size)
    try:
        for doc in cursor:
            # 按照格式转换
            doc = build_kafka_message(doc)
            doc = convert_bytes(doc)  # 新增：转换 bytes 字段
            producer.send(kafka_topic, doc)
            count += 1
            if count >= limit:
                break
        producer.flush()
        print(f"成功发送 {count} 条数据到 Kafka topic: {kafka_topic}")
    finally:
        cursor.close()
        client.close()
        producer.close()


# 示例用法（请根据实际情况修改参数）
if __name__ == "__main__":
    mongo_uri = "**********************************************************************************************************************************"
    db_name = "lynxiao_feature"
    collection_name = "dev_IDX_S1NG44L3"
    kafka_servers = ["10.103.240.121:9093", "10.103.240.122:9093", "10.103.240.123:9093"]
    kafka_topic = "lynxiao-asset-index-sink-dx"
    batch_size = 100
    limit = 1000
    if len(sys.argv) > 6:
        mongo_uri = sys.argv[1]
        db_name = sys.argv[2]
        collection_name = sys.argv[3]
        kafka_servers = sys.argv[4].split(',')
        kafka_topic = sys.argv[5]
        batch_size = int(sys.argv[6])
        limit = int(sys.argv[7]) if len(sys.argv) > 7 else limit
    mongo_to_kafka(mongo_uri, db_name, collection_name, kafka_servers, kafka_topic, batch_size, limit)
