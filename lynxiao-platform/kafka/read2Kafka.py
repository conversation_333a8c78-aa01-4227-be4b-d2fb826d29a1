"""
 读取 json文件内容，发送到Kafka指定topic

 <AUTHOR> 2025/6/26 16:38
"""

import json
import sys

from kafka import KafkaProducer


def json_to_kafka(json_path, kafka_servers, kafka_topic, batch_size=100, limit=1000):
    producer = KafkaProducer(
        bootstrap_servers=kafka_servers,
        value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
    )
    count = 0
    with open(json_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            try:
                data = json.loads(line)
                producer.send(kafka_topic, data)
                count += 1
                if count % batch_size == 0:
                    producer.flush()
                if count >= limit:
                    break
            except Exception as e:
                print(f"解析或发送失败: {e}, 内容: {line}")
    producer.flush()
    producer.close()
    print(f"成功发送 {count} 条数据到 Kafka topic: {kafka_topic}")


# 用法: python read2Kafka.py <json_path> <kafka_servers> <kafka_topic> [batch_size] [limit]

if __name__ == "__main__":
    json_path = "./data/read.json"
    kafka_servers = ["**************:9093", "**************:9093", "**************:9093"]
    kafka_topic = "lynxiao-asset-index-sink-dx"
    batch_size = 100
    limit = 1000
    if len(sys.argv) > 4:
        json_path = sys.argv[1]
        kafka_servers = sys.argv[2].split(',')
        kafka_topic = sys.argv[3]
        batch_size = int(sys.argv[4]) if len(sys.argv) > 4 else 100
        limit = int(sys.argv[5]) if len(sys.argv) > 5 else 1000

    json_to_kafka(json_path, kafka_servers, kafka_topic, batch_size, limit)
