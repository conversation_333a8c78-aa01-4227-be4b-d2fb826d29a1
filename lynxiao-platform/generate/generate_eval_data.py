import random
import openpyxl
import os


def generate_records(input_file, output_file, target_count=1000):
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"文件 {input_file} 不存在，请检查路径！")
        return

    # 加载Excel文件
    workbook = openpyxl.load_workbook(input_file)
    sheet = workbook.active

    # 获取表头字段
    headers = [cell.value for cell in sheet[1]]  # 第1行是表头
    if "query" not in headers:
        print("输入文件缺少 'query' 字段，请检查！")
        return

    # 找到query列的索引，用于后续操作
    query_col_index = headers.index("query")

    # 获取已有数据行
    original_rows = list(sheet.iter_rows(values_only=True))[1:]  # 跳过表头

    if len(original_rows) == 0:
        print("输入文件中无有效数据行，请检查！")
        return

    # 计算需要扩展的记录数量
    current_count = len(original_rows)
    if current_count >= target_count:
        print(f"文件已经包含 {current_count} 条记录，不需要额外生成。")
        return

    # 随机生成记录
    new_records = []
    for _ in range(target_count - current_count):
        random_row = list(random.choice(original_rows))  # 随机选一行数据
        random_suffix = random.randint(10000, 99999)  # 拼接5位随机数
        query_value = random_row[query_col_index]  # 获取原始query值

        # 确保query字段拼接随机数
        random_row[query_col_index] = f"{query_value}_{random_suffix}"
        new_records.append(random_row)

    # 将新记录追加到工作簿
    for record in new_records:
        sheet.append(record)

    # 保存到输出文件
    workbook.save(output_file)
    print(f"已生成 {target_count} 条记录，并保存到文件: {output_file}")


if __name__ == "__main__":
    # 输入文件路径
    input_file = "./data/badcase.xlsx"  # 请将 input.xlsx 替换为你实际的文件路径
    output_file = "./data/generate/badcase.xlsx"  # 输出文件路径
    target_count = 1000  # 目标记录总数

    generate_records(input_file, output_file, target_count)
