# -*- coding:utf-8 -*-
# Author: jlxia3
import json

from elasticsearch import Elasticsearch

trace_id_list = ["61cb9e12b7a2111177711"]

if __name__ == "__main__":

    index = ["lynxiao_flow-2024.12.27"]
    for i in range(len(index)):
        file_name = index[i] + "_flow.txt"
        with open(file_name, "w", encoding="utf-8") as file:
            pass  # 不写入任何内容，文件会被清空

        # es = Elasticsearch(["http://elk-es.elk-bj02-zjeekv.svc.bjb.ipaas.cn:9200"], http_auth=("elastic","111qqq..."))
        # 上海elk
        # es = Elasticsearch(["http://************:19200"], http_auth=("only_read", "1qaz@wsx"))
        # 合肥elk
        es = Elasticsearch(["http://elk-es.elk-hf04-mhwn9e.svc.hfb.ipaas.cn:9200"], http_auth=("elastic", "d7mhXaGqsBe65xp0bZdn"))

        for trace_id in trace_id_list:
            body = {
                "query": {
                    "term": {
                        "traceId.keyword": {
                            "value": trace_id
                        }
                    }
                },
                "size": 100
            }

            query = es.search(index=index[i], body=body)
            print(query)
            results = query['hits']['hits']

            for data in results:
                strData = json.dumps(data['_source'], ensure_ascii=False)
                with open(file_name, "a", encoding="utf-8") as file:
                    file.write(strData + "\n")

    print("查询结束", index[i])
