import base64
import hashlib
import hmac
import json
import time
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time

import pandas as pd
import requests

"""
运营产品使用脚本，从数据集读取问题批量处理
"""

TOKEN_SECRET = "E14387BE795D1D34747AD698"
TRACE_ID = "test-{}".format(int(time.time()))
SEARCH_API = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/search"
APP_ID = "ry819lag"
API_SECRET = "8962BC42C6894FF282E0BBA8"
REGION = "hf"
PROD_CODE = "Search"  # 必须保证在业务应用管理-关联产品里是启用状态，如不是启用状态需更换为一个启用状态的产品编码（可能需替换的内容）
# 场景策略版本流程id（需替换的内容）
PROCESS_ID = "67a5bea0cd0c1b11c336e694"
PV_NAME = "xxxx"


# --------------------- lynxiao-search-monitor.py ---------------------

def assemble_auth_header(url, method, api_key, api_secret):
    """
    接口鉴权方法
    """
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def assemble_token(app_id: str, prod_code: str, region: str, pvid: str, pv_name: str) -> str:
    """
    token生成方法
    """
    # 生成13位时间戳
    timestamp = str(int(time.time() * 1000))

    # 构建签名字符串
    builder = f"appId: {app_id}\nprodCode: {prod_code}\ndate: {timestamp}\nregion: {region}\npvid: {pvid}\n"

    # 创建HMAC对象
    hmac_obj = hmac.new(TOKEN_SECRET.encode("utf-8"), msg=builder.encode("utf-8"), digestmod=hashlib.sha256)

    # 进行签名
    hex_digits = hmac_obj.digest()

    # 进行Base64编码
    signature = base64.b64encode(hex_digits).decode("utf-8")

    # 生成 token
    token_json = {
        "appId": app_id,
        "date": timestamp,
        "prodCode": prod_code,
        "pvid": pvid,
        "pvName": pv_name,
        "region": region,
        "signature": signature
    }
    return base64.b64encode(json.dumps(token_json).encode('utf-8')).decode('utf-8')


def call_search(query_str: str) -> list:
    try:
        # 组装请求头和请求体
        headers = assemble_auth_header(SEARCH_API, "POST", APP_ID, API_SECRET)
        search_body = {
            "header": {
                "traceId": TRACE_ID,
                "appId": APP_ID,
                "prodCode": PROD_CODE,
                "token": assemble_token(APP_ID, PROD_CODE, REGION, PROCESS_ID, PV_NAME),
            },
            "parameter": {"id": PROCESS_ID},
            "payload": {
                "query": [
                    query_str
                ]
            }
        }

        print(search_body)  # Debug

        # 发送请求
        response = requests.post(SEARCH_API, headers=headers, json=search_body, timeout=30)
        response.raise_for_status()  # 检查 HTTP 请求状态
        response_json = response.json()

        print(response_json)

        # 解析响应
        header = response_json.get('header', {})
        code = header.get('code', "1")

        payload = response_json.get('payload', {})
        output = payload.get('output', {})
        results = output.get('data', [])

        result = []
        if code == 0:
            for item in results:
                for doc in item["docs"]:
                    result_item = {
                        "query": query_str,
                        "content": doc.get("content", ""),
                        "title": doc.get("title", ""),
                        "url": doc.get("protocol", "") + "://" + doc.get("domain", "") + doc.get("path", ""),
                    }
                    result.append(result_item)

        return result

    except Exception as e:
        # 捕获所有其他异常
        print(e)
        return []


def read_queries() -> list:
    file_path = "./file/权威知识测评217.xlsx"  # 替换为你的 Excel 文件路径
    df = pd.read_excel(file_path)

    # 提取指定列的数据
    column_name = "query"  # 替换为你需要提取的列名
    if column_name in df.columns:
        query_texts = df[column_name].dropna().tolist()  # 去掉 NaN 值并转化为数组
        print(query_texts)  # 输出结果数组
        return query_texts

    return []


if __name__ == "__main__":
    query_list = read_queries()
    result_data = []  # 初始化一个空列表来存储所有结果

    for query in query_list:
        data = call_search(query)
        print(data)
        result_data.extend(data)  # 将每次循环的结果追加到列表中

    # 将结果列表转换为 DataFrame
    df_result = pd.DataFrame(result_data)

    # 保存到Excel文件
    result_file_path = "./file/测试结果.xlsx"  # 替换为你要保存的 Excel 文件路径
    df_result.to_excel(result_file_path, index=False)
