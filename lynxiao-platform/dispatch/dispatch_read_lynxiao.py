import base64
import hashlib
import hmac
import json
import time
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time

import requests

"""
按照分流，搜索方式调用
"""

ROUTE_API_URL = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/route"

TRACE_ID = "bbding_test"
# 1: 验证环境 2：生产环境
ENV_TYPE = 2

app_id = "zysstest"
api_secret = "7C1C0728C52C48ACBD7E9376"

prod_code = "HealthySearch"

route_payload = {"idc": "dx"}


def assemble_auth_header(url, method, api_key, api_secret):
    """
    接口鉴权方法
    """
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def call_route() -> dict:
    headers = assemble_auth_header(ROUTE_API_URL, "POST", app_id, api_secret)
    route_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "envType": ENV_TYPE
        },
        "payload": route_payload
    }
    print()
    print(route_request_data)
    start_time = time.time()
    response = requests.post(ROUTE_API_URL, headers=headers, json=route_request_data, timeout=3)
    response_json = response.json()
    print(f"分流：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")

    # 解析响应
    header = response_json.get('header', {})
    if header['code'] != 0:
        raise Exception(f"分流失败：{header['message']}")

    return response_json['payload']


def call_search(route_result: dict, query: str) -> dict:
    search_url = route_result['regionUrl']
    if route_result['regionCode'] == "sh":
        search_url = route_result['regionOuterUrl']

    headers = assemble_auth_header(search_url, "POST", app_id, api_secret)
    search_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "token": route_result['token']
        },
        "parameter": {
            "id": route_result['processId']
        },
        "payload": {
            "query": [
                query
            ],
            "intent": "1",
            "appId": "cc501f15",
            "topK": 5

            # "scene": ["诊疗-治疗方案-其他"],
            # "fromType": "晓医"

        }
    }

    start_time = time.time()
    print(f"搜索请求：{json.dumps(search_request_data, ensure_ascii=False, indent=4)}")
    response = requests.post(search_url, headers=headers, json=search_request_data, timeout=10)
    response_json = response.json()
    print(f"搜索：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")

    # 解析响应
    header = response_json.get('header', {})
    if header['code'] != 0:
        raise Exception(f"搜索失败：{header['message']}")

    return response_json['payload']


def compute_cost(start_time):
    end_time = time.time()
    elapsed_time_ms = (end_time - start_time) * 1000
    return f"{elapsed_time_ms:.2f} ms"


if __name__ == "__main__":
    querys = open("./file/query_2k.json", "r", encoding="utf-8").readlines()
    res_write = open("./file/测试结果_hu.json", "a+", encoding="utf-8")
    for query in querys:
        route_response = call_route()
        result = call_search(route_response, query)
        res_write.write(json.dumps(result, ensure_ascii=False))
        res_write.write("\n")
