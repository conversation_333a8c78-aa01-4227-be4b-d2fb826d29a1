import base64
import hashlib
import hmac
import json
import random
import requests
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time


def count_overlap(list1, list2):
    set1 = set(list1)
    # 使用集合的交集操作来找出重合的元素个数
    overlap_count = len(set(list1) & set(list2))
    return overlap_count


def assemble_auth_header(url, method, api_key, api_secret):
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def fenliu(appid, appserat):
    data = {"header": {"traceId": "206b0e7e676f", "appId": appid, "prodCode": "HealthySearch", "envType": 1},
            "payload": {"idc": "hf"}}
    url = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/route"
    headers = assemble_auth_header(url, "POST", appid, appserat)
    res = requests.post(url=url, json=data, headers=headers).content
    results = json.loads(res)
    print(results)
    return results


index = ["lynxiao_flow-2025.02.25"]


def req_lynxiao_hf(text, traceId, token, id):
    data = {"header": {"traceId": traceId, "appId": "zysstest", "prodCode": "HealthySearch", "token": token},
            "parameter": {"id": id}, "payload": {"intent": "1", "appId": "cc501f15", "topK": 5, "query": [text]}}
    url = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/search"
    headers = assemble_auth_header(url, "POST", "zysstest", "7C1C0728C52C48ACBD7E9376")
    print(data)
    results = json.loads(requests.post(url=url, data=json.dumps(data), headers=headers).content)
    results["query"] = text
    print(results)
    return results


id = "67eca900518c5fbb429a5190"


def lynxiao_write(id):
    querys = open("./file/query_2k.json", "r", encoding="utf-8").readlines()
    res_write = open("测试结果_hu.json", "a+", encoding="utf-8")
    for i in range(len(querys)):
        print(i)
        text = querys[i].replace("\n", "")
        traceId = "testqinnigdiwgew" + str(i) + str(random.randint(0, 1000000))
        res = fenliu("zysstest", "7C1C0728C52C48ACBD7E9376")
        result = req_lynxiao_hf(text, traceId, res["payload"]["token"], id)
        res_write.write(json.dumps(result, ensure_ascii=False))
        res_write.write("\n")


lynxiao_write(id)
