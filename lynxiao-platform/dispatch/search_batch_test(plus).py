import base64
import hashlib
import hmac
import json
import re
import time
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time

import pandas as pd
import requests

"""
运营产品使用脚本，从数据集读取问题批量处理
"""

TOKEN_SECRET = "E14387BE795D1D34747AD698"
TRACE_ID = "test-{}".format(int(time.time()))
# 生产环境
SEARCH_API = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/search"
APP_ID = "zysstest"
API_SECRET = "7C1C0728C52C48ACBD7E9376"
REGION = "hf"
PROD_CODE = "HealthySearch"
# 产品方案版本名称
PV_NAME = "bbding_test"
# 场景策略版本流程id
PROCESS_ID = "68197b7b518c5327b9e704fb"


# 开发环境
# SEARCH_API = "http://10.103.240.170:31600/dispatch/api/v1/search"
# APP_ID = "cc501f15"
# API_SECRET = "11E9750C72E849F28FA8ED00"
# REGION = "sh"
# PROD_CODE = "HealthySearch"
# # 产品方案版本名称
# PV_NAME = "xxxx"
# # 场景策略版本流程id
# PROCESS_ID = "67d23e856f8e7e517d0b4c62"


# 新增清洗函数
def clean_excel_text(text):
    # 移除所有ASCII控制字符（0x00-0x1F，除了\t\n\r）
    return re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f]', '', str(text))


# --------------------- lynxiao-search-monitor.py ---------------------

def assemble_auth_header(url, method, api_key, api_secret):
    """
    接口鉴权方法
    """
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def assemble_token(app_id: str, prod_code: str, region: str, pvid: str, pv_name: str) -> str:
    """
    token生成方法
    """
    # 生成13位时间戳
    timestamp = str(int(time.time() * 1000))

    # 构建签名字符串
    builder = f"appId: {app_id}\nprodCode: {prod_code}\ndate: {timestamp}\nregion: {region}\npvid: {pvid}\n"

    # 创建HMAC对象
    hmac_obj = hmac.new(TOKEN_SECRET.encode("utf-8"), msg=builder.encode("utf-8"), digestmod=hashlib.sha256)

    # 进行签名
    hex_digits = hmac_obj.digest()

    # 进行Base64编码
    signature = base64.b64encode(hex_digits).decode("utf-8")

    # 生成 token
    token_json = {
        "appId": app_id,
        "date": timestamp,
        "prodCode": prod_code,
        "pvid": pvid,
        "pvName": pv_name,
        "region": region,
        "signature": signature
    }
    return base64.b64encode(json.dumps(token_json).encode('utf-8')).decode('utf-8')


def call_search(query_str: str) -> list:
    try:
        # 组装请求头和请求体
        headers = assemble_auth_header(SEARCH_API, "POST", APP_ID, API_SECRET)
        search_body = {
            "header": {
                "traceId": TRACE_ID,
                "appId": APP_ID,
                "prodCode": PROD_CODE,
                "token": assemble_token(APP_ID, PROD_CODE, REGION, PROCESS_ID, PV_NAME),
                # "token": "eyJhcHBJZCI6InJ5ODE5bGFnIiwiZGF0ZSI6MTczOTc5MzQ5MTM4NSwicHJvZENvZGUiOiJTZWFyY2giLCJwdk5hbWUiOiLmmJ/ngavmkJzntKJf5Yy755aX5pCc57SiXzAxMTYoVjA1MikiLCJwdmlkIjoiMTg4MDEzMjE1NTE0NTg3MTM2MCIsInJlZ2lvbiI6ImhmIiwic2lnbmF0dXJlIjoibnpidDhqdFJ2ZEZQNk5CT0NKSkRkTXB6bUI1NmgrM2UySEs5azc5ZkVXVT0ifQ=="

            },
            "parameter": {"id": PROCESS_ID},
            "payload": {
                "appId": APP_ID,
                "query": [
                    query_str
                ],
                "intent": "1",
                "scene": ["诊疗-治疗方案-其他"],
                "topK": 5
            }
        }

        print(search_body)  # Debug

        # 发送请求
        response = requests.post(SEARCH_API, headers=headers, json=search_body, timeout=30)
        response.raise_for_status()  # 检查 HTTP 请求状态
        response_json = response.json()

        print(response_json)
        # json_string = json.dumps(response_json, ensure_ascii=False, indent=4)
        # print(json_string)

        # 解析响应
        header = response_json.get('header', {})
        code = header.get('code', "1")

        payload = response_json.get('payload', {})
        output = payload.get('output', {})

        if output.get("output") is not None:
            output = output.get('output', {})

        # 流程的输出变量
        # results = output.get('data', [])
        results = output.get('results', [])
        result = []
        if code == 0:
            for item in results:
                for doc in item["docs"]:
                    result_item = {
                        "query": query_str,
                        "content": doc.get("content", ""),
                        "title": doc.get("title", ""),
                        "url": doc.get("protocol", "") + "://" + doc.get("domain", "") + doc.get("path", ""),
                        "rank_score": doc.get("_rank_score", 0.0),
                    }
                    result.append(result_item)

        return result

    except Exception as e:
        # 捕获所有其他异常
        print(e)
        return []


def read_queries() -> list:
    file_path = "./file/权威知识测评.xlsx"  # 替换为你的 Excel 文件路径
    df = pd.read_excel(file_path)

    # 提取指定列的数据
    column_name = "query"  # 替换为你需要提取的列名
    if column_name in df.columns:
        query_texts = df[column_name].dropna().tolist()  # 去掉 NaN 值并转化为数组
        print(query_texts)  # 输出结果数组
        return query_texts

    return []


if __name__ == "__main__":
    query_list = read_queries()
    result_data = []  # 初始化一个空列表来存储所有结果

    for query in query_list:
        data = call_search(query)
        print(data)
        result_data.extend(data)  # 将每次循环的结果追加到列表中

    # 将结果列表转换为 DataFrame
    df_result = pd.DataFrame(result_data)

    df_result = df_result.applymap(clean_excel_text)

    # 保存到Excel文件
    result_file_path = "./file/测试结果.xlsx"
    df_result.to_excel(result_file_path, index=False)
