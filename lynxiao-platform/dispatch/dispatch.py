import base64
import hashlib
import hmac
import json
import time
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time

import requests

"""
按照分流，搜索方式调用
"""

# ROUTE_API_URL = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/route"
# ROUTE_API_URL = "http://localhost:31600/dispatch/api/v1/route"
# ROUTE_API_URL = "http://**************:31600/dispatch/api/v1/route"
# ROUTE_API_URL = "http://localhost:31600/dispatch/api/v1/route"
ROUTE_API_URL = "http://**************:30023/v1/route"

TRACE_ID = "bbding_test"
# 1: 验证环境 2：生产环境
ENV_TYPE = 1

app_id = "zysstest"
api_secret = "7C1C0728C52C48ACBD7E9376"

# app_id = "gz5upxdx"
# api_secret = "235A2E955D95490986CEB3BD"

# app_id = "5glndm76"
# api_secret = "7836C2074CDF4B16ABBA800B"

# ---------------------
# 医疗搜索
prod_code = "HealthySearch"
route_payload = {"idc": "sh"}
search_payload = {
    "query": [
        "糖尿病了怎么办？"
    ]
}


# ----------------------
# 汽车搜索
# prod_code = "car_search_prod"
# route_payload = {"idc": "sh"}
# search_payload = {
#     "query": [
#         "问界汽车有哪些功能？"
#     ],
#     # "topK": 15
# }


# ----------------------
# 凌霄知道
# prod_code = "lynxiao_know_prod"
# route_payload = {"idc": "hf"}
# search_payload = {
#     "query":
#         "监控指标"
#     ,
#     # "topK": 5
# }


def assemble_auth_header(url, method, api_key, api_secret):
    """
    接口鉴权方法
    """
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def call_route() -> dict:
    headers = assemble_auth_header(ROUTE_API_URL, "POST", app_id, api_secret)
    route_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "envType": ENV_TYPE
        },
        "payload": route_payload
    }
    print()
    print(route_request_data)
    start_time = time.time()
    response = requests.post(ROUTE_API_URL, headers=headers, json=route_request_data, timeout=3)
    response_json = response.json()
    print(f"分流：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")

    # 解析响应
    header = response_json.get('header', {})
    if header['code'] != 0:
        raise Exception(f"分流失败：{header['message']}")

    return response_json['payload']


def call_search(route_result: dict) -> dict:
    search_url = route_result['regionUrl']
    if route_result['regionCode'] == "sh":
        search_url = route_result['regionOuterUrl']

    headers = assemble_auth_header(search_url, "POST", app_id, api_secret)
    search_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "token": route_result['token']
        },
        "parameter": {
            "id": route_result['processId']
        },
        "payload": search_payload
    }

    start_time = time.time()
    print(f"搜索请求：{json.dumps(search_request_data, ensure_ascii=False, indent=4)}")
    response = requests.post(search_url, headers=headers, json=search_request_data, timeout=10)
    response_json = response.json()
    print(f"搜索：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")

    # 解析响应
    header = response_json.get('header', {})
    if header['code'] != 0:
        raise Exception(f"搜索失败：{header['message']}")

    return response_json['payload']


def compute_cost(start_time):
    end_time = time.time()
    elapsed_time_ms = (end_time - start_time) * 1000
    return f"{elapsed_time_ms:.2f} ms"


if __name__ == "__main__":
    for i in range(1):
        route_response = call_route()
        call_search(route_response)
