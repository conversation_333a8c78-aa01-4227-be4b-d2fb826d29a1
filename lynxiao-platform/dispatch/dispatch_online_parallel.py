import base64
import hashlib
import hmac
import json
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from time import mktime
from urllib.parse import urlparse
from wsgiref.handlers import format_date_time

import requests

"""
按照分流，搜索方式调用
"""

ROUTE_API_URL = "http://lynxiao-search-api-hf-internal.xf-yun.com/v1/route"
# ROUTE_API_URL = "http://*************:30013/v1/route"
# ROUTE_API_URL = "http://*************:31604/dispatch/api/v1/route"
# ROUTE_API_URL = "http://**************:31602/dispatch/api/v1/route"
# ROUTE_API_URL = "http://localhost:31600/dispatch/api/v1/route"
# ROUTE_API_URL = "http://**************:31600/dispatch/api/v1/route"
# ROUTE_API_URL = "http://**************:30013/v1/route"

# ROUTE_API_URL = "http://**************:31600/dispatch/api/v1/route"

TRACE_ID = "bbding-test"
# 1: 验证环境 2：生产环境
ENV_TYPE = 1

# 自测账号
app_id = "zysstest"
api_secret = "7C1C0728C52C48ACBD7E9376"

# 星火搜索
# prod_code = "Search";
# route_payload = {"idc": "sh"}
# search_payload = {
#     "query": [
#         "牙周炎"
#     ],
#     "appId": "cc501f15",
#     # "appId": "b4b7d678",
#     "scene": ["诊疗-治疗方案-其他"]
# }


# ------医疗搜索
# prod_code = "HealthySearch";
# route_payload = {"idc": "hf"}
# search_payload = {
#     "query": [
#         "济宁"
#     ],
#     "appId": "cc501f15",
#     # 1：精品库（可能走领域分类）； 2：权威知识库； 3：聚合搜索
#     "intent": "1",
#     # 不走领域分类
#     "scene": ["诊疗-治疗方案-其他"],
#     # scene不设置值，走领域分类
#     # "scene": [],
#     "topK": 5
# }


prod_code = "HealthySearch"

route_payload = {"idc": "hf"}
search_payload = {
    "query": [
        "感冒了吃什么药？"
    ],
    "appId": "cc501f15",
    "intent": "1",
    "topK": 5
}


#
# prod_code = "medical"
#
# route_payload = {"idc": "hf"}
# search_payload = {
#     "query": [
#         "感冒了吃什么药？"
#     ],
#     "appId": "cc501f15",
#     "intent": "1",
#     "topK": 5
# }

# ------晓知
# prod_code = "AISearch";
# route_payload = {}
# search_payload = {
#     "query": [
#         "人工智能发展"
#     ],
# }

def assemble_auth_header(url, method, api_key, api_secret):
    """
    接口鉴权方法
    """
    u = urlparse(url)
    host = u.hostname
    path = u.path
    now = datetime.now()
    date = format_date_time(mktime(now.timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(host, date, method, path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization = "hmac api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    headers = {
        "host": host,
        "date": date,
        "authorization": authorization,
    }
    return headers


def call_route() -> dict:
    headers = assemble_auth_header(ROUTE_API_URL, "POST", app_id, api_secret)
    route_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "envType": ENV_TYPE
        },
        "payload": route_payload
    }
    start_time = time.time()
    response = requests.post(ROUTE_API_URL, headers=headers, json=route_request_data, timeout=3)
    response_json = response.json()
    print(f"分流：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")

    # 解析响应
    header = response_json.get('header', {})
    if header['code'] != 0:
        raise Exception(f"分流失败：{header['message']}")

    return response_json['payload']


def call_search(route_result: dict):
    # 使用公网域名
    search_url = route_result['regionInnerUrl']
    if route_result['regionCode'] == "dx" or route_result['regionCode'] == "sh":
        search_url = route_result['regionOuterUrl']
    # search_url = "http://*************:31604/dispatch/api/v1/search"

    headers = assemble_auth_header(search_url, "POST", app_id, api_secret)
    search_request_data = {
        "header": {
            "traceId": f"{TRACE_ID}{int(time.time())}",
            "appId": app_id,
            "prodCode": prod_code,
            "token": route_result['token']
        },
        "parameter": {
            "id": route_result['processId']
        },
        "payload": search_payload
    }

    start_time = time.time()
    print(f"搜索请求：{json.dumps(search_request_data, ensure_ascii=False, indent=4)}")
    response = requests.post(search_url, headers=headers, json=search_request_data, timeout=10)
    response_json = response.json()
    print(f"搜索：cost={compute_cost(start_time)}, result={json.dumps(response_json, ensure_ascii=False, indent=4)}")
    print(f"搜索：cost={compute_cost(start_time)}")

    # 解析响应
    header = response_json.get('header', {})
    # if header['code'] != 0:
    if header.get('code') != 0:
        print(f"搜索请求：{json.dumps(search_request_data, ensure_ascii=False, indent=4)}")
        print(f"搜索结果：{json.dumps(response_json, ensure_ascii=False, indent=4)}")
        raise Exception(f"搜索失败：{header['message']}")

    end_time = time.time()
    elapsed_time_ms = (end_time - start_time) * 1000
    return elapsed_time_ms


def compute_cost(start_time):
    end_time = time.time()
    elapsed_time_ms = (end_time - start_time) * 1000
    return f"{elapsed_time_ms:.2f} ms"


if __name__ == "__main__":
    # 参数配置
    max_workers = 10  # 线程池最大工作线程数
    loop_per_route = 100  # 总共处理次数
    top_percent_to_trim = 0.1  # 去掉最大的10%

    route_response = call_route()
    costs = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for _ in range(loop_per_route):
            future = executor.submit(call_search, route_response)
            futures.append(future)

        for future in futures:
            result = future.result()
            costs.append(result)
            print(f"搜索完成，耗时：{result}")

    # 去掉最大的10%的数据
    sorted_costs = sorted(costs)
    trim_index = int(len(sorted_costs) * (1 - top_percent_to_trim))
    trimmed_costs = sorted_costs[:trim_index]

    average = sum(trimmed_costs) / len(trimmed_costs)
    print(f"去掉最大的{top_percent_to_trim * 100}%的数据后，平均耗时为：{average} ms")
