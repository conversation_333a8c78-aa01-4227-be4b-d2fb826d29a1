
import pymysql
import logging
from pymysql import Error

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """
    获取数据库连接
    """
    config = {
        'host': '**************',
        'port': 23312,
        'user': 'u_lynxiao',  # 注意：这里使用的是只读用户，实际删除操作需要写权限用户
        'password': 'I4ZTw228sy8IHpL6oHjr',
        'database': 'lynxiao_portal',  # 请根据实际数据库名称修改
        'charset': 'utf8mb4'
    }

    try:
        connection = pymysql.connect(**config)
        logger.info("数据库连接成功")
        return connection
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def clean_orphaned_task_executions():
    """
    遍历asset_task_execution表中的每一条记录，
    如果记录中的task_id在asset_task表中不存在，则删除该记录。
    """
    connection = None
    cursor = None
    deleted_count = 0

    try:
        # 获取数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 开始事务
        connection.begin()

        # 查询所有孤立的task_execution记录
        # 使用LEFT JOIN找出在asset_task表中不存在对应task_id的记录
        query_orphaned = """
        SELECT ate.id, ate.task_id
        FROM asset_task_execution ate
        LEFT JOIN asset_task at ON ate.task_id = at.id
        WHERE at.id IS NULL
        """

        logger.info("开始查询孤立的task_execution记录...")
        cursor.execute(query_orphaned)
        orphaned_records = cursor.fetchall()

        if not orphaned_records:
            logger.info("没有找到孤立的task_execution记录")
            return 0

        logger.info(f"找到 {len(orphaned_records)} 条孤立记录")

        # 批量删除孤立记录
        if orphaned_records:
            # 提取所有孤立记录的ID
            orphaned_ids = [str(record[0]) for record in orphaned_records]

            # 构建批量删除SQL
            placeholders = ','.join(['%s'] * len(orphaned_ids))
            delete_query = f"DELETE FROM asset_task_execution WHERE id IN ({placeholders})"

            # 执行删除操作
            cursor.execute(delete_query, orphaned_ids)
            deleted_count = cursor.rowcount

            # 提交事务
            connection.commit()
            logger.info(f"成功删除 {deleted_count} 条孤立记录")

            # 记录被删除的记录详情（可选）
            for record in orphaned_records:
                logger.debug(f"删除记录: execution_id={record[0]}, task_id={record[1]}")

    except Error as e:
        # 发生错误时回滚事务
        if connection:
            connection.rollback()
        logger.error(f"删除操作失败: {e}")
        raise

    finally:
        # 关闭数据库连接
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

    return deleted_count

def verify_cleanup():
    """
    验证清理结果，检查是否还有孤立记录
    """
    connection = None
    cursor = None

    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        # 再次查询孤立记录
        verify_query = """
        SELECT COUNT(*)
        FROM asset_task_execution ate
        LEFT JOIN asset_task at ON ate.task_id = at.id
        WHERE at.id IS NULL
        """

        cursor.execute(verify_query)
        remaining_count = cursor.fetchone()[0]

        if remaining_count == 0:
            logger.info("验证通过：没有剩余的孤立记录")
        else:
            logger.warning(f"验证失败：仍有 {remaining_count} 条孤立记录")

        return remaining_count

    except Error as e:
        logger.error(f"验证操作失败: {e}")
        raise

    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """
    主函数
    """
    try:
        logger.info("开始清理孤立的task_execution记录...")

        # 执行清理操作
        deleted_count = clean_orphaned_task_executions()

        # 验证清理结果
        remaining_count = verify_cleanup()

        logger.info(f"清理完成！删除了 {deleted_count} 条记录，剩余孤立记录: {remaining_count} 条")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())