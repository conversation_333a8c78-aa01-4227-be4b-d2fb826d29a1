import pandas as pd
import pymysql
from pymysql import Error

def query_index_codes(excel_path, output_path):
    # 读取Excel文件（注意列名改为'code'）
    try:
        df = pd.read_excel(excel_path)
        index_codes = df['code'].tolist()  # 确保Excel中有'code'列
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return

    # 修正数据库配置
    config = {
        'host': 'mkn284zgr7u0.mysql.hf01.dbaas.private',  # 移除端口号
        'port': 23314,  # 添加端口参数（整数类型）
        'user': 'u_lynxiao_read',
        'password': 'GhLFpVB5B8LGt98oQcw0',
        'database': 'lynxiao_index_v2',
        'charset': 'utf8mb4'
    }

    results = []
    try:
        conn = pymysql.connect(**config)
        cursor = conn.cursor()

        for code in index_codes:
            try:
                cursor.execute(
                    "SELECT id, index_code FROM index_infos WHERE index_code = %s",
                    (code,)
                )
                row = cursor.fetchone()
                if row:
                    # 修正结果处理：row是元组 (id, index_code)
                    results.append({'id': row, 'index_code': row})
                else:
                    results.append({'id': '未找到', 'index_code': code})
            except Error as e:
                print(f"查询失败: {code} - {e}")
                continue

    except Error as e:
        print(f"数据库连接失败: {e}")
    finally:
        if 'conn' in locals() and conn.open:
            cursor.close()
            conn.close()

    result_df = pd.DataFrame(results)
    result_df.to_excel(output_path, index=False)
    print(f"查询完成，结果已保存到: {output_path}")

if __name__ == "__main__":
    query_index_codes("./data/input.xlsx", "output.xlsx")
